# 多模态识别模型骨干网络替换任务

## 任务概述
将 MultiModalRecognizer 模型中的 MobileNetV2TSM 骨干网络模块替换为 ResNetTSM 模块

## 核心修改方案
- **骨干网络替换**：MobileNetV2TSM → ResNetTSM (ResNet50)
- **特征维度调整**：320维 → 512维 (使用layer2输出)
- **特征比例平衡**：姿态256维 vs 图像512维 (2:1比例)

## 实施计划

### 步骤1：创建新配置文件
- 文件：`configs/recognition/Multimodal/multimodal_poseGCN-resnet_tsm_fusion.py`
- 修改：ResNetTSM配置，out_indices=(1,)，img_feat_dim=512

### 步骤2：创建新模型文件  
- 文件：`mmaction/models/recognizers/multimodal_recognizer_2.py`
- 修改：支持ResNetTSM特征维度检测和处理

### 步骤3：验证测试
- 配置文件加载测试
- 模型初始化验证
- 前向传播测试

## 技术细节
- ResNet50 layer2输出：512维特征
- 保持原有多模态融合架构
- 支持后续扩展到1024/2048维

## 预期效果
- 特征表达能力提升60% (320→512维)
- 保持多模态特征平衡
- 支持正常训练和推理
