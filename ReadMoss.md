created @Moss 2025/02/06 11:10

#### 环境安装
cd mmaction2
pip install -v -e .
* "-v" 表示输出更多安装相关的信息
* "-e" 表示以可编辑形式安装，这样可以在不重新安装的情况下，让本地修改直接生效。


#### 使用 `tools/train.py` 在一台带有 CPU 和 GPU(可选) 的单机上训练模型
0. 预训练模型 下载
https://github.com/open-mmlab/mmaction2/tree/main/configs/skeleton/posec3d

1. 新增 训练体育项目的脚本
 tools/train_sport.sh

2.配置文件
 configs/skeleton/posec3d/slowonly_r50_sport-keypoint.py

3. 数据准备及介绍

   提供生成 NTURGB+D 数据集中视频的姿态标注文件，这将生成一个 dict 数据并将其保存为一个 pickle 文件。
用户可以生成一个 list 用以包含对应视频的 dict 数据，并将其保存为一个 pickle 文件。
之后，用户可以获得 `ntu60_xsub_train.pkl`, `ntu60_xsub_val.pkl`, `ntu120_xsub_train.pkl`, `ntu120_xsub_val.pkl` 文件用于训练。

##### PoseC3D 的标注文件格式

这里简单介绍 PoseC3D 的标注文件格式。以 `gym_train.pkl` 为例：
`gym_train.pkl` 存储一个长度为 20484 的 list，list 的每一项为单个视频的骨架标注 dict。每个 dict 的内容如下：

- keypoint：关键点坐标，大小为 N（#人数）x T（时序长度）x K（#关键点, 这里为17）x 2 （x，y 坐标）的 numpy array 数据类型
- keypoint_score：关键点的置信分数，大小为 N（#人数）x T（时序长度）x K（#关键点, 这里为17）的 numpy array 数据类型
- frame_dir: 对应视频名
- label: 动作类别
- img_shape: 每一帧图像的大小
- original_shape: 同 `img_shape`
- total_frames: 视频时序长度

如用户想使用自己的数据集训练 PoseC3D，可以参考
[Custom Dataset Training]
(https://github.com/open-mmlab/mmaction2/blob/master/configs/skeleton/posec3d/custom_dataset_training.md)

### 使用 PoseC3D 进行自定义数据集训练
您需要利用 Prepare Annotations 中所示的
ntu_pose_extraction.py 提取自定义数据集中每个视频的 2D 关键点。
该命令如下所示（假设您的视频名称为 ）： some_video_from_my_dataset.mp4
```
# 您可以使用上述命令为所有训练和验证视频生成 pickle 文件。
# 使用此脚本从自定义视频数据集中提取姿势时，您可以跳过ntu_det_postproc步骤
python ntu_pose_extraction.py some_video_from_my_dataset.mp4 some_video_from_my_dataset.pkl
```
然后，您将所有 pickle 文件收集到一个列表中进行训练（当然，用于验证）并将它们保存为单个文件（如 或 ）。
此时，您将完成为自定义数据集准备注释文件。custom_dataset_train.pklcustom_dataset_val.pkl

* 进行训练，如 PoseC3D/Train 所示：
python tools/train.py configs/skeleton/posec3d/slowonly_r50_u48_240e_ntu120_xsub_keypoint.py \ 
                      --work-dir work_dirs/slowonly_r50_u48_240e_ntu120_xsub_keypoint \
                      --validate --test-best --gpus 2 --seed 0 --deterministic

在运行上述脚本之前，您需要修改变量以使用新创建的 annotation 文件进行初始化：
```
model = dict(
    ...
    cls_head=dict(
        ...
        num_classes=4,    # Your class number
        ...
    ),
    ...
)

ann_file_train = 'data/posec3d/custom_dataset_train.pkl'  # Your annotation for training
ann_file_val = 'data/posec3d/custom_dataset_val.pkl'      # Your annotation for validation

load_from = 'pretrained_weight.pth'       # Your can use released weights for initialization, set to None if training from scratch

# You can also alter the hyper parameters or training schedule
```

#### 为了可视化骨架数据，用户需要准备 RGB 的视频。详情可参考 \[visualize_heatmap_volume\]
