#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ResNetTSM替换MobileNetV2TSM的实现
"""

import sys
import os
sys.path.insert(0, '.')

def test_config_loading():
    """测试配置文件加载"""
    print("=" * 50)
    print("测试1: 配置文件加载")
    print("=" * 50)
    
    try:
        from mmengine import Config
        
        # 测试新配置文件
        cfg = Config.fromfile('configs/recognition/Multimodal/multimodal_poseGCN-resnet_tsm_fusion.py')
        print("✅ 新配置文件加载成功!")
        
        # 验证关键配置
        assert cfg.model.image_backbone.type == 'ResNetTSM', f"Expected ResNetTSM, got {cfg.model.image_backbone.type}"
        assert cfg.model.image_backbone.depth == 50, f"Expected depth=50, got {cfg.model.image_backbone.depth}"
        assert cfg.model.image_backbone.out_indices == (1,), f"Expected out_indices=(1,), got {cfg.model.image_backbone.out_indices}"
        assert cfg.model.fusion_neck.img_feat_dim == 512, f"Expected img_feat_dim=512, got {cfg.model.fusion_neck.img_feat_dim}"
        
        print(f"  - Image backbone: {cfg.model.image_backbone.type}")
        print(f"  - Depth: {cfg.model.image_backbone.depth}")
        print(f"  - Out indices: {cfg.model.image_backbone.out_indices}")
        print(f"  - Image feat dim: {cfg.model.fusion_neck.img_feat_dim}")
        print(f"  - Pose feat dim: {cfg.model.fusion_neck.pose_feat_dim}")
        print(f"  - Fusion dim: {cfg.model.fusion_neck.fusion_dim}")
        print(f"  - Work dir: {cfg.work_dir}")
        
        # 对比原配置文件
        try:
            cfg_orig = Config.fromfile('configs/recognition/Multimodal/multimodal_poseGCN-rgbR50_fusion.py')
            print("\n📊 配置对比:")
            print(f"  原配置 - Image backbone: {cfg_orig.model.image_backbone.type}")
            print(f"  新配置 - Image backbone: {cfg.model.image_backbone.type}")
            print(f"  原配置 - Image feat dim: {cfg_orig.model.fusion_neck.img_feat_dim}")
            print(f"  新配置 - Image feat dim: {cfg.model.fusion_neck.img_feat_dim}")
        except:
            print("  (无法加载原配置文件进行对比)")
            
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_syntax():
    """测试模型文件语法"""
    print("\n" + "=" * 50)
    print("测试2: 模型文件语法检查")
    print("=" * 50)
    
    try:
        import py_compile
        py_compile.compile('mmaction/models/recognizers/multimodal_recognizer_2.py', doraise=True)
        print("✅ 模型文件语法检查通过!")
        return True
    except Exception as e:
        print(f"❌ 模型文件语法错误: {e}")
        return False

def test_feature_dimension_logic():
    """测试特征维度计算逻辑"""
    print("\n" + "=" * 50)
    print("测试3: 特征维度计算逻辑")
    print("=" * 50)
    
    try:
        # 模拟特征维度计算
        def determine_resnet_feat_dim(depth, out_indices):
            resnet_feat_dims = {
                0: 256,   # layer1
                1: 512,   # layer2  
                2: 1024,  # layer3
                3: 2048   # layer4
            }
            
            if len(out_indices) == 1:
                return resnet_feat_dims.get(out_indices[0], 2048)
            else:
                return resnet_feat_dims.get(max(out_indices), 2048)
        
        # 测试不同配置
        test_cases = [
            (50, (1,), 512),   # layer2
            (50, (2,), 1024),  # layer3
            (50, (3,), 2048),  # layer4
            (50, (0,), 256),   # layer1
        ]
        
        for depth, out_indices, expected in test_cases:
            result = determine_resnet_feat_dim(depth, out_indices)
            assert result == expected, f"Expected {expected}, got {result} for out_indices={out_indices}"
            print(f"  ✅ ResNet{depth} out_indices={out_indices} -> {result}维")
        
        print("✅ 特征维度计算逻辑正确!")
        return True
        
    except Exception as e:
        print(f"❌ 特征维度计算逻辑错误: {e}")
        return False

def test_file_existence():
    """测试文件是否存在"""
    print("\n" + "=" * 50)
    print("测试4: 文件存在性检查")
    print("=" * 50)
    
    files_to_check = [
        'configs/recognition/Multimodal/multimodal_poseGCN-resnet_tsm_fusion.py',
        'mmaction/models/recognizers/multimodal_recognizer_2.py',
        'issues/多模态识别模型骨干网络替换任务.md'
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (不存在)")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("🚀 开始测试ResNetTSM替换实现...")
    
    results = []
    results.append(test_file_existence())
    results.append(test_config_loading())
    results.append(test_model_syntax())
    results.append(test_feature_dimension_logic())
    
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    test_names = [
        "文件存在性检查",
        "配置文件加载",
        "模型文件语法检查", 
        "特征维度计算逻辑"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n📊 总体结果: {success_count}/{total_count} 测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！ResNetTSM替换实现成功！")
        print("\n📝 后续步骤:")
        print("1. 在完整环境中测试模型初始化")
        print("2. 进行前向传播测试")
        print("3. 验证训练流程")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现")
        return False

if __name__ == '__main__':
    main()
