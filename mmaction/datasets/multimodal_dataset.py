# -*-coding:utf-8-*-
from typing import Callable, Dict, List, Optional, Union
import os.path as osp

import mmengine
from mmengine.logging import MMLogger

import numpy as np
from mmaction.registry import DATASETS
from mmaction.datasets.base import BaseActionDataset


@DATASETS.register_module()
class PoseRgbDataset(BaseActionDataset):
    """双模态数据集，包含姿态点序列和图像"""

    def __init__(self,
                 ann_file,
                 pipeline: List[Union[Dict, Callable]],
                 split: Optional[str] = None,
                 valid_ratio: Optional[float] = None,
                 **kwargs):
        self.split = split
        self.valid_ratio = valid_ratio

        super().__init__(ann_file, pipeline=pipeline, modality='Pose', **kwargs)

    def load_annotations(self):
        """加载标注文件"""
        video_infos = []
        with open(self.ann_file, 'r') as f:
            for line in f:
                line_split = line.strip().split()
                video_info = dict()

                # 解析数据路径和标签
                video_info['pose_file'] = line_split[0]  # 姿态点文件路径
                video_info['img_file'] = line_split[1]  # 图像文件路径
                video_info['label'] = int(line_split[2])  # 标签

                # 设置帧数等信息
                video_info['total_frames'] = 40
                video_info['start_index'] = 0

                video_infos.append(video_info)

        return video_infos

    def prepare_train_frames(self, idx):
        """准备训练数据"""
        results = self.video_infos[idx].copy()
        results['modality'] = 'Pose+RGB'
        return self.pipeline(results)

    def prepare_test_frames(self, idx):
        """准备测试数据"""
        results = self.video_infos[idx].copy()
        results['modality'] = 'Pose+RGB'
        return self.pipeline(results)

    def load_data_list(self) -> List[Dict]:
        """Load annotation file to get skeleton information."""
        assert self.ann_file.endswith('.pkl')
        mmengine.exists(self.ann_file)
        data_list = mmengine.load(self.ann_file)

        if self.split is not None:
            split, annos = data_list['split'], data_list['annotations']
            identifier = 'filename' if 'filename' in annos[0] else 'frame_dir'
            split = set(split[self.split])
            # dict_keys(['keypoint', 'keypoint_score', 'img_shape', 'original_shape', 'total_frames', 'vid_frames', 'frame_dir', 'filename', 'label'])
            data_list = [x for x in annos if x[identifier] in split]
        else:
            annos = data_list['annotations']
            # dict_keys(['keypoint', 'keypoint_score', 'img_shape', 'original_shape', 'total_frames', 'vid_frames', 'frame_dir', 'filename', 'label'])
            data_list = [x for x in annos]

        return data_list

    def filter_data(self) -> List[Dict]:
        """Filter out invalid samples."""
        if self.valid_ratio is not None and isinstance(
                self.valid_ratio, float) and self.valid_ratio > 0:
            self.data_list = [
                x for x in self.data_list if x['valid'][self.box_thr] /
                x['total_frames'] >= self.valid_ratio
            ]
            for item in self.data_list:
                assert 'box_score' in item,\
                    'if valid_ratio is a positive number,' \
                    'item should have field `box_score`'
                anno_inds = (item['box_score'] >= self.box_thr)
                item['anno_inds'] = anno_inds

        logger = MMLogger.get_current_instance()
        logger.info(
            f'{len(self.data_list)} videos remain after valid thresholding')

        return self.data_list

    def get_data_info(self, idx: int) -> Dict:
        """Get annotation by index."""
        data_info = super().get_data_info(idx)

        # Sometimes we may need to load skeleton from the file
        if 'skeleton' in self.data_prefix:
            identifier = 'filename' if 'filename' in data_info \
                else 'frame_dir'
            ske_name = data_info[identifier]
            ske_path = osp.join(self.data_prefix['skeleton'],
                                ske_name + '.pkl')
            ske = mmengine.load(ske_path)
            for k in ske:
                data_info[k] = ske[k]

        return data_info



@DATASETS.register_module()
class LoadPose2D:
    """加载2D姿态序列数据"""

    def __init__(self,
                 pose_format='npy',
                 num_keypoints=17,
                 coord_dim=3):
        self.pose_format = pose_format
        self.num_keypoints = num_keypoints
        self.coord_dim = coord_dim

    def __call__(self, results):
        """加载姿态数据"""
        pose_file = results['pose_file']

        if self.pose_format == 'npy':
            pose_data = np.load(pose_file)  # (T, V, C)
        elif self.pose_format == 'json':
            # 处理JSON格式的姿态数据
            import json
            with open(pose_file, 'r') as f:
                pose_json = json.load(f)
            pose_data = self._parse_json_pose(pose_json)
        else:
            raise ValueError(f"Unsupported pose format: {self.pose_format}")

        # 验证数据格式
        assert pose_data.shape[1] == self.num_keypoints, \
            f"Expected {self.num_keypoints} keypoints, got {pose_data.shape[1]}"
        assert pose_data.shape[2] == self.coord_dim, \
            f"Expected {self.coord_dim} coordinates, got {pose_data.shape[2]}"

        results['keypoint'] = pose_data
        results['total_frames'] = pose_data.shape[0]

        return results

    def _parse_json_pose(self, pose_json):
        """解析JSON格式的姿态数据"""
        # 根据具体的JSON格式实现解析逻辑
        # 这里提供一个示例实现
        frames = []
        for frame_data in pose_json['annotations']:
            keypoints = np.array(frame_data['keypoints']).reshape(-1, 3)
            frames.append(keypoints)
        return np.array(frames)


@DATASETS.register_module()
class PoseNormalize:
    """姿态数据归一化"""

    def __init__(self,
                 img_shape=(224, 224),
                 norm_by_imgshape=True):
        self.img_shape = img_shape
        self.norm_by_imgshape = norm_by_imgshape

    def __call__(self, results):
        """归一化姿态坐标"""
        keypoint = results['keypoint']  # (T, V, 3)

        if self.norm_by_imgshape:
            # 按图像尺寸归一化
            keypoint[..., 0] /= self.img_shape[1]  # x坐标
            keypoint[..., 1] /= self.img_shape[0]  # y坐标
            # 置信度保持不变
        else:
            # 按数据本身的范围归一化
            x_coords = keypoint[..., 0]
            y_coords = keypoint[..., 1]

            x_min, x_max = x_coords.min(), x_coords.max()
            y_min, y_max = y_coords.min(), y_coords.max()

            if x_max > x_min:
                keypoint[..., 0] = (x_coords - x_min) / (x_max - x_min)
            if y_max > y_min:
                keypoint[..., 1] = (y_coords - y_min) / (y_max - y_min)

        results['keypoint'] = keypoint
        return results


@DATASETS.register_module()
class PoseSequenceSample:
    """姿态序列采样"""

    def __init__(self,
                 clip_len=40,
                 sampling_strategy='uniform'):
        self.clip_len = clip_len
        self.sampling_strategy = sampling_strategy

    def __call__(self, results):
        """采样姿态序列"""
        keypoint = results['keypoint']  # (T, V, 3)
        total_frames = keypoint.shape[0]

        if total_frames >= self.clip_len:
            if self.sampling_strategy == 'uniform':
                # 均匀采样
                indices = np.linspace(0, total_frames - 1, self.clip_len, dtype=int)
            elif self.sampling_strategy == 'random':
                # 随机连续采样
                start_idx = np.random.randint(0, total_frames - self.clip_len + 1)
                indices = np.arange(start_idx, start_idx + self.clip_len)
            else:
                raise ValueError(f"Unknown sampling strategy: {self.sampling_strategy}")

            sampled_keypoint = keypoint[indices]
        else:
            # 重复填充
            repeat_times = self.clip_len // total_frames + 1
            repeated_keypoint = np.tile(keypoint, (repeat_times, 1, 1))
            sampled_keypoint = repeated_keypoint[:self.clip_len]

        results['keypoint'] = sampled_keypoint
        return results